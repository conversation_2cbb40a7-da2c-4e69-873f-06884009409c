# FastAPI Application

A FastAPI application with PostgreSQL database integration.

## Cấu trúc dự án

```
fastapi-project
├── alembic/
├── src
│   ├── auth
│   │   ├── router.py
│   │   ├── schemas.py  # pydantic models
│   │   ├── models.py  # db models
│   │   ├── dependencies.py
│   │   ├── config.py  # local configs
│   │   ├── constants.py
│   │   ├── exceptions.py
│   │   ├── service.py
│   │   └── utils.py
│   ├── aws
│   │   ├── client.py  # client model for external service communication
│   │   ├── schemas.py
│   │   ├── config.py
│   │   ├── constants.py
│   │   ├── exceptions.py
│   │   └── utils.py
│   └── posts
│   │   ├── router.py
│   │   ├── schemas.py
│   │   ├── models.py
│   │   ├── dependencies.py
│   │   ├── constants.py
│   │   ├── exceptions.py
│   │   ├── service.py
│   │   └── utils.py
│   ├── config.py  # global configs
│   ├── models.py  # global models
│   ├── exceptions.py  # global exceptions
│   ├── pagination.py  # global module e.g. pagination
│   ├── database.py  # db connection related stuff
│   └── main.py
├── tests/
│   ├── auth
│   ├── aws
│   └── posts
├── templates/
│   └── index.html
├── requirements
│   ├── base.txt
│   ├── dev.txt
│   └── prod.txt
├── .env
├── .gitignore
├── logging.ini
└── alembic.ini
```

## Setup

# Tạo virtual environment với Python 3.11/3.12
python -m venv .venv
# Hoặc: py -3.12 -m venv .venv

# Kích hoạt virtual environment
.venv\Scripts\activate

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Configure environment variables:
Copy `.env.example` to `.env` and update the database settings:
```
DATABASE_URL=********************************************
DATABASE_USER=user
DATABASE_PASSWORD=password
DATABASE_HOST=postgresserver
DATABASE_NAME=db
```

3. Run the application:
```bash
uvicorn main:app --reload
```

## API Endpoints

- `GET /` - Hello World endpoint

## Database

The application uses PostgreSQL with SQLAlchemy ORM. Database models are defined in `models.py`.